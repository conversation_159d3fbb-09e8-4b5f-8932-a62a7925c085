import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

interface SaveUserDataRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  location: {
    city: string;
    governorate: string;
  };
  servicePreferences?: string[];
  projectTypes?: string[];
  businessInfo?: {
    companyName: string;
    industry: string;
    size: string;
  };
  role: 'EXPERT' | 'CLIENT' | 'BUSINESS';
}

interface SaveUserDataResponse {
  success: boolean;
  message: string;
  data?: {
    userId: string;
    dataCollected: boolean;
    nextStep: string;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SaveUserDataResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized - No valid session'
      });
    }

    const {
      firstName,
      lastName,
      email,
      phoneNumber,
      location,
      servicePreferences,
      projectTypes,
      businessInfo,
      role
    }: SaveUserDataRequest = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !phoneNumber || !location?.city || !location?.governorate || !role) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Enhanced location validation - allow custom locations
    if (location.governorate !== 'أخرى' && !validateSyrianLocation(location.governorate, location.city)) {
      console.warn('⚠️ Non-Syrian location provided:', location);
      // Don't fail validation for international locations, just log it
    }

    // Enhanced phone number validation for international numbers
    const phoneWithoutSpaces = phoneNumber.replace(/\s/g, '');

    // More flexible international validation
    const isValidInternational = /^(\+[1-9]\d{6,14})$/.test(phoneWithoutSpaces);
    const isValidSyrian = /^(\+963|0)?[0-9]{8,9}$/.test(phoneWithoutSpaces);

    // Additional validation for common formats
    const isValidLocalFormat = /^[0-9]{7,15}$/.test(phoneWithoutSpaces); // Local format without country code

    console.log('📞 Phone validation debug:', {
      original: phoneNumber,
      cleaned: phoneWithoutSpaces,
      isValidInternational,
      isValidSyrian,
      isValidLocalFormat
    });

    if (!isValidInternational && !isValidSyrian && !isValidLocalFormat) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format. Please use a valid phone number.'
      });
    }

    // Role-specific validation
    if (role === 'EXPERT' && (!servicePreferences || servicePreferences.length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'Service preferences are required for experts'
      });
    }

    if (role === 'CLIENT' && (!projectTypes || projectTypes.length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'Project types are required for clients'
      });
    }

    if (role === 'BUSINESS' && (!businessInfo?.companyName || !businessInfo?.industry || !businessInfo?.size)) {
      return res.status(400).json({
        success: false,
        message: 'Complete business information is required for business accounts'
      });
    }

    // TODO: Save to database
    // For now, we'll simulate saving the data
    console.log('💾 Saving user data:', {
      userId: session.user.id,
      firstName,
      lastName,
      email,
      phoneNumber,
      location,
      role,
      servicePreferences,
      projectTypes,
      businessInfo
    });

    // Simulate database save
    await new Promise(resolve => setTimeout(resolve, 500));

    // TODO: Update user record in database with collected data
    /*
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        firstName,
        lastName,
        phoneNumber,
        governorate: location.governorate,
        city: location.city,
        role,
        dataCollected: true,
        // Add other fields as needed
      }
    });

    // Create role-specific profiles
    if (role === 'EXPERT') {
      await prisma.expertProfile.create({
        data: {
          userId: session.user.id,
          serviceCategories: servicePreferences,
          // Add other expert-specific fields
        }
      });
    } else if (role === 'CLIENT') {
      await prisma.clientProfile.create({
        data: {
          userId: session.user.id,
          projectTypes,
          // Add other client-specific fields
        }
      });
    } else if (role === 'BUSINESS') {
      await prisma.businessProfile.create({
        data: {
          userId: session.user.id,
          companyName: businessInfo.companyName,
          industry: businessInfo.industry,
          size: businessInfo.size,
          // Add other business-specific fields
        }
      });
    }
    */

    // Try to notify the main API about data collection completion
    try {
      const apiResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3001'}/api/onboarding/user-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.user.id}`,
        },
        body: JSON.stringify({
          userId: session.user.id,
          firstName,
          lastName,
          email,
          phoneNumber,
          location,
          role,
          servicePreferences,
          projectTypes,
          businessInfo
        }),
      });

      if (apiResponse.ok) {
        console.log('✅ Successfully notified main API about data collection');
      } else {
        console.warn('⚠️ Failed to notify main API, but continuing...');
      }
    } catch (error) {
      console.error('❌ Error notifying main API:', error);
      // Don't fail the request if this fails
    }

    return res.status(200).json({
      success: true,
      message: 'User data saved successfully',
      data: {
        userId: session.user.id,
        dataCollected: true,
        nextStep: 'ai_introduction'
      }
    });

  } catch (error) {
    console.error('❌ Error saving user data:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Helper function to validate Syrian governorates and cities
function validateSyrianLocation(governorate: string, city: string): boolean {
  const SYRIAN_LOCATIONS: Record<string, string[]> = {
    'دمشق': ['دمشق', 'داريا', 'دوما', 'جرمانا', 'قدسيا'],
    'ريف دمشق': ['الزبداني', 'قطنا', 'التل', 'يبرود', 'النبك'],
    'حلب': ['حلب', 'منبج', 'عفرين', 'اعزاز', 'الباب'],
    'حمص': ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة'],
    'حماة': ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية'],
    'اللاذقية': ['اللاذقية', 'جبلة', 'القرداحة', 'الحفة'],
    'طرطوس': ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش'],
    'إدلب': ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان'],
    'الحسكة': ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية'],
    'دير الزور': ['دير الزور', 'الميادين', 'البوكمال'],
    'الرقة': ['الرقة', 'تل أبيض', 'الثورة'],
    'درعا': ['درعا', 'إزرع', 'الصنمين', 'نوى'],
    'السويداء': ['السويداء', 'شهبا', 'صلخد', 'القريا'],
    'القنيطرة': ['القنيطرة', 'فيق', 'خان أرنبة']
  };

  const cities = SYRIAN_LOCATIONS[governorate];
  return cities && cities.includes(city);
}
