import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'next-i18next';

interface DataCollectionFormProps {
  onSubmit: (data: any) => void;
  isLoading?: boolean;
  selectedRole: 'EXPERT' | 'CLIENT' | 'BUSINESS';
  initialData?: any;
}

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  location: {
    city: string;
    governorate: string;
  };
  servicePreferences?: string[];
  projectTypes?: string[];
  businessInfo?: {
    companyName: string;
    industry: string;
    size: string;
  };
}

interface DataCollectionFormProps {
  onSubmit: (data: any) => void;
  onBack?: () => void; // Add back navigation prop
  isLoading?: boolean;
  selectedRole: 'EXPERT' | 'CLIENT' | 'BUSINESS';
  initialData?: any;
}

export function DataCollectionForm({
  onSubmit,
  onBack,
  isLoading = false,
  selectedRole,
  initialData
}: DataCollectionFormProps) {
  const { t } = useTranslation('common');

  // Fix: Use local loading state for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    firstName: initialData?.firstName || '',
    lastName: initialData?.lastName || '',
    email: initialData?.email || '',
    phoneNumber: initialData?.phoneNumber || '',
    location: {
      city: initialData?.location?.city || '',
      governorate: initialData?.location?.governorate || '',
    },
    servicePreferences: initialData?.servicePreferences || [],
    projectTypes: initialData?.projectTypes || [],
    businessInfo: initialData?.businessInfo || {
      companyName: '',
      industry: '',
      size: '',
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Enhanced location state for international support
  const [showCustomLocation, setShowCustomLocation] = useState(false);
  const [customLocation, setCustomLocation] = useState('');

  // Enhanced phone state for international support
  const [selectedCountryCode, setSelectedCountryCode] = useState('+963'); // Default to Syria

  // International country codes (most common ones)
  const COUNTRY_CODES = [
    { code: '+963', name: 'سوريا', flag: '🇸🇾' },
    { code: '+1', name: 'الولايات المتحدة/كندا', flag: '🇺🇸' },
    { code: '+44', name: 'المملكة المتحدة', flag: '🇬🇧' },
    { code: '+49', name: 'ألمانيا', flag: '🇩🇪' },
    { code: '+33', name: 'فرنسا', flag: '🇫🇷' },
    { code: '+39', name: 'إيطاليا', flag: '🇮🇹' },
    { code: '+34', name: 'إسبانيا', flag: '🇪🇸' },
    { code: '+31', name: 'هولندا', flag: '🇳🇱' },
    { code: '+46', name: 'السويد', flag: '🇸🇪' },
    { code: '+47', name: 'النرويج', flag: '🇳🇴' },
    { code: '+45', name: 'الدنمارك', flag: '🇩🇰' },
    { code: '+41', name: 'سويسرا', flag: '🇨🇭' },
    { code: '+43', name: 'النمسا', flag: '🇦🇹' },
    { code: '+32', name: 'بلجيكا', flag: '🇧🇪' },
    { code: '+971', name: 'الإمارات', flag: '🇦🇪' },
    { code: '+966', name: 'السعودية', flag: '🇸🇦' },
    { code: '+962', name: 'الأردن', flag: '🇯🇴' },
    { code: '+961', name: 'لبنان', flag: '🇱🇧' },
    { code: '+20', name: 'مصر', flag: '🇪🇬' },
    { code: '+90', name: 'تركيا', flag: '🇹🇷' },
    { code: '+98', name: 'إيران', flag: '🇮🇷' },
    { code: '+964', name: 'العراق', flag: '🇮🇶' },
    { code: '+965', name: 'الكويت', flag: '🇰🇼' },
    { code: '+974', name: 'قطر', flag: '🇶🇦' },
    { code: '+973', name: 'البحرين', flag: '🇧🇭' },
    { code: '+968', name: 'عمان', flag: '🇴🇲' },
    { code: '+212', name: 'المغرب', flag: '🇲🇦' },
    { code: '+213', name: 'الجزائر', flag: '🇩🇿' },
    { code: '+216', name: 'تونس', flag: '🇹🇳' },
    { code: '+218', name: 'ليبيا', flag: '🇱🇾' },
    { code: '+249', name: 'السودان', flag: '🇸🇩' },
  ];

  // Syrian governorates and major cities
  const SYRIAN_LOCATIONS = {
    'دمشق': ['دمشق', 'داريا', 'دوما', 'جرمانا', 'قدسيا'],
    'ريف دمشق': ['الزبداني', 'قطنا', 'التل', 'يبرود', 'النبك'],
    'حلب': ['حلب', 'منبج', 'عفرين', 'اعزاز', 'الباب'],
    'حمص': ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة'],
    'حماة': ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية'],
    'اللاذقية': ['اللاذقية', 'جبلة', 'القرداحة', 'الحفة'],
    'طرطوس': ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش'],
    'إدلب': ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان'],
    'الحسكة': ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية'],
    'دير الزور': ['دير الزور', 'الميادين', 'البوكمال', 'الرقة'],
    'الرقة': ['الرقة', 'تل أبيض', 'الثورة'],
    'درعا': ['درعا', 'إزرع', 'الصنمين', 'نوى'],
    'السويداء': ['السويداء', 'شهبا', 'صلخد', 'القريا'],
    'القنيطرة': ['القنيطرة', 'فيق', 'خان أرنبة']
  };

  const SERVICE_CATEGORIES = {
    EXPERT: [
      'تطوير المواقع',
      'التصميم الجرافيكي',
      'التسويق الرقمي',
      'الترجمة',
      'المحاسبة',
      'الاستشارات القانونية',
      'التصوير',
      'الكتابة والتحرير',
      'البرمجة',
      'التدريس',
      'الخدمات الهندسية',
      'الخدمات الطبية',
      'الكهرباء والصيانة',
      'السباكة',
      'النجارة',
      'البناء والتشييد'
    ],
    CLIENT: [
      'مشاريع تقنية',
      'تصميم وإبداع',
      'تسويق ومبيعات',
      'خدمات تجارية',
      'استشارات',
      'خدمات منزلية',
      'خدمات طبية',
      'تعليم وتدريب',
      'خدمات قانونية',
      'خدمات مالية'
    ]
  };

  const BUSINESS_INDUSTRIES = [
    'التكنولوجيا',
    'التجارة الإلكترونية',
    'التعليم',
    'الصحة',
    'العقارات',
    'السياحة',
    'الصناعة',
    'الزراعة',
    'الخدمات المالية',
    'الإعلام والاتصالات',
    'النقل واللوجستيات',
    'الطاقة',
    'أخرى'
  ];

  const BUSINESS_SIZES = [
    'شركة ناشئة (1-10 موظفين)',
    'شركة صغيرة (11-50 موظف)',
    'شركة متوسطة (51-200 موظف)',
    'شركة كبيرة (200+ موظف)'
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'الاسم الأول مطلوب';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'الاسم الأخير مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'رقم الهاتف مطلوب';
    } else {
      // Enhanced phone validation for international numbers
      const phoneWithoutSpaces = formData.phoneNumber.replace(/\s/g, '');

      // Create the full international format for validation
      let fullPhoneNumber = phoneWithoutSpaces;

      // If not Syrian and doesn't already have country code, add it
      if (selectedCountryCode !== '+963' && !phoneWithoutSpaces.startsWith('+')) {
        fullPhoneNumber = `${selectedCountryCode}${phoneWithoutSpaces}`;
      }

      // Validation patterns
      const isValidInternational = /^(\+[1-9]\d{6,14})$/.test(fullPhoneNumber);
      const isValidSyrian = /^(\+963|0)?[0-9]{8,9}$/.test(phoneWithoutSpaces);

      // For Syrian numbers, use Syrian validation
      // For international numbers, use international validation with proper formatting
      const isValid = selectedCountryCode === '+963' ? isValidSyrian : isValidInternational;

      if (!isValid) {
        newErrors.phoneNumber = selectedCountryCode === '+963'
          ? 'رقم الهاتف السوري غير صحيح (مثال: 0912345678)'
          : 'رقم الهاتف غير صحيح';
      }
    }

    if (!formData.location.governorate) {
      newErrors.governorate = 'المحافظة مطلوبة';
    }

    if (!formData.location.city) {
      newErrors.city = 'المدينة مطلوبة';
    }

    // Role-specific validation
    if (selectedRole === 'EXPERT' && (!formData.servicePreferences || formData.servicePreferences.length === 0)) {
      newErrors.servicePreferences = 'يجب اختيار مجال خبرة واحد على الأقل';
    }

    if (selectedRole === 'CLIENT' && (!formData.projectTypes || formData.projectTypes.length === 0)) {
      newErrors.projectTypes = 'يجب اختيار نوع مشروع واحد على الأقل';
    }

    if (selectedRole === 'BUSINESS') {
      if (!formData.businessInfo?.companyName.trim()) {
        newErrors.companyName = 'اسم الشركة مطلوب';
      }
      if (!formData.businessInfo?.industry) {
        newErrors.industry = 'مجال الشركة مطلوب';
      }
      if (!formData.businessInfo?.size) {
        newErrors.size = 'حجم الشركة مطلوب';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      setIsSubmitting(true); // Fix: Use local loading state
      setErrors({});

      // Prepare form data with enhanced phone number (include country code)
      let formattedPhoneNumber = formData.phoneNumber.replace(/\s/g, '');

      // Format phone number for API submission
      if (selectedCountryCode !== '+963') {
        // For international numbers, ensure proper formatting
        if (!formattedPhoneNumber.startsWith('+')) {
          formattedPhoneNumber = `${selectedCountryCode} ${formattedPhoneNumber}`;
        }
      } else {
        // For Syrian numbers, keep as is (can be with or without +963/0)
        formattedPhoneNumber = formData.phoneNumber;
      }

      const submissionData = {
        ...formData,
        phoneNumber: formattedPhoneNumber,
        location: showCustomLocation
          ? { city: customLocation, governorate: 'أخرى' }
          : formData.location,
        role: selectedRole
      };

      // Save user data to API
      const response = await fetch('/api/onboarding/save-user-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to save user data');
      }

      if (data.success) {
        // Call the parent onSubmit with the collected data
        onSubmit(submissionData);
      } else {
        throw new Error(data.message || 'Failed to save user data');
      }
    } catch (error: any) {
      console.error('Failed to save user data:', error);
      setErrors({
        submit: error.message || 'حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.'
      });
    } finally {
      setIsSubmitting(false); // Fix: Use local loading state
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleLocationChange = (field: 'governorate' | 'city', value: string) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value,
        // Reset city when governorate changes
        ...(field === 'governorate' && { city: '' })
      }
    }));
    
    // Clear errors
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleBusinessInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      businessInfo: {
        ...prev.businessInfo!,
        [field]: value
      }
    }));
    
    // Clear error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleArrayFieldChange = (field: 'servicePreferences' | 'projectTypes', value: string) => {
    setFormData(prev => {
      const currentArray = prev[field] || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      
      return {
        ...prev,
        [field]: newArray
      };
    });
    
    // Clear error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    
    if (cleaned.startsWith('963')) {
      const number = cleaned.substring(3);
      return `+963 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    } else if (cleaned.startsWith('0')) {
      const number = cleaned.substring(1);
      return `0${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    }
    
    return phone;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="max-w-4xl mx-auto p-6"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          إكمال المعلومات الشخصية
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {selectedRole === 'EXPERT' && 'أكمل معلوماتك لإنشاء ملف خبير احترافي'}
          {selectedRole === 'CLIENT' && 'أكمل معلوماتك للعثور على أفضل الخبراء'}
          {selectedRole === 'BUSINESS' && 'أكمل معلومات شركتك لإدارة المشاريع بكفاءة'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            المعلومات الشخصية
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الاسم الأول *
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.firstName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="أدخل الاسم الأول"
                disabled={isLoading}
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الاسم الأخير *
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.lastName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="أدخل الاسم الأخير"
                disabled={isLoading}
              />
              {errors.lastName && (
                <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Phone Number with Country Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                رقم الهاتف *
              </label>
              <div className="flex gap-2">
                {/* Country Code Dropdown */}
                <select
                  value={selectedCountryCode}
                  onChange={(e) => setSelectedCountryCode(e.target.value)}
                  className="w-32 px-3 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white border-gray-300"
                  disabled={isLoading || isSubmitting}
                  aria-label="رمز البلد"
                >
                  {COUNTRY_CODES.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.flag} {country.code}
                    </option>
                  ))}
                </select>

                {/* Phone Number Input */}
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => {
                    const value = e.target.value;
                    // Remove any existing country code from input
                    const cleanValue = value.replace(/^\+\d+\s*/, '');
                    handleInputChange('phoneNumber', cleanValue);
                  }}
                  className={`flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder={selectedCountryCode === '+963' ? "XX XXX XXXX" : "رقم الهاتف"}
                  disabled={isLoading || isSubmitting}
                />
              </div>
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
              )}
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {selectedCountryCode === '+963'
                  ? 'أدخل رقم هاتف سوري صحيح'
                  : 'أدخل رقم الهاتف بدون رمز البلد'}
              </p>
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            معلومات الموقع
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Governorate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المحافظة *
              </label>
              <select
                value={showCustomLocation ? 'أخرى' : formData.location.governorate}
                onChange={(e) => {
                  if (e.target.value === 'أخرى') {
                    setShowCustomLocation(true);
                    handleLocationChange('governorate', 'أخرى');
                  } else {
                    setShowCustomLocation(false);
                    handleLocationChange('governorate', e.target.value);
                  }
                }}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.governorate ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading || isSubmitting}
                aria-label="المحافظة"
              >
                <option value="">اختر المحافظة</option>
                {Object.keys(SYRIAN_LOCATIONS).map((governorate) => (
                  <option key={governorate} value={governorate}>
                    {governorate}
                  </option>
                ))}
                <option value="أخرى">أخرى (خارج سوريا)</option>
              </select>
              {errors.governorate && (
                <p className="mt-1 text-sm text-red-600">{errors.governorate}</p>
              )}
            </div>

            {/* City */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المدينة *
              </label>

              {showCustomLocation ? (
                <input
                  type="text"
                  value={customLocation}
                  onChange={(e) => {
                    setCustomLocation(e.target.value);
                    handleLocationChange('city', e.target.value);
                  }}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.city ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="أدخل اسم المدينة والبلد"
                  disabled={isLoading || isSubmitting}
                />
              ) : (
                <select
                  value={formData.location.city}
                  onChange={(e) => handleLocationChange('city', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.city ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading || isSubmitting || !formData.location.governorate}
                  aria-label="المدينة"
                >
                  <option value="">اختر المدينة</option>
                  {formData.location.governorate &&
                    SYRIAN_LOCATIONS[formData.location.governorate as keyof typeof SYRIAN_LOCATIONS]?.map((city) => (
                      <option key={city} value={city}>
                        {city}
                      </option>
                    ))
                  }
                </select>
              )}

              {errors.city && (
                <p className="mt-1 text-sm text-red-600">{errors.city}</p>
              )}
              {!showCustomLocation && !formData.location.governorate && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  اختر المحافظة أولاً
                </p>
              )}
              {showCustomLocation && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  مثال: لندن، المملكة المتحدة
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Role-specific sections */}
        {selectedRole === 'EXPERT' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              مجالات الخبرة
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                اختر مجالات خبرتك (يمكن اختيار أكثر من مجال) *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {SERVICE_CATEGORIES.EXPERT.map((service) => (
                  <label
                    key={service}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      formData.servicePreferences?.includes(service)
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.servicePreferences?.includes(service) || false}
                      onChange={() => handleArrayFieldChange('servicePreferences', service)}
                      className="sr-only"
                      disabled={isLoading}
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {service}
                    </span>
                  </label>
                ))}
              </div>
              {errors.servicePreferences && (
                <p className="mt-2 text-sm text-red-600">{errors.servicePreferences}</p>
              )}
            </div>
          </div>
        )}

        {selectedRole === 'CLIENT' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              أنواع المشاريع
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                ما نوع المشاريع التي تحتاج إليها؟ (يمكن اختيار أكثر من نوع) *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {SERVICE_CATEGORIES.CLIENT.map((projectType) => (
                  <label
                    key={projectType}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      formData.projectTypes?.includes(projectType)
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.projectTypes?.includes(projectType) || false}
                      onChange={() => handleArrayFieldChange('projectTypes', projectType)}
                      className="sr-only"
                      disabled={isLoading}
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {projectType}
                    </span>
                  </label>
                ))}
              </div>
              {errors.projectTypes && (
                <p className="mt-2 text-sm text-red-600">{errors.projectTypes}</p>
              )}
            </div>
          </div>
        )}

        {selectedRole === 'BUSINESS' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              معلومات الشركة
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Company Name */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم الشركة *
                </label>
                <input
                  type="text"
                  value={formData.businessInfo?.companyName || ''}
                  onChange={(e) => handleBusinessInfoChange('companyName', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.companyName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="أدخل اسم الشركة"
                  disabled={isLoading}
                />
                {errors.companyName && (
                  <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>
                )}
              </div>

              {/* Industry */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مجال الشركة *
                </label>
                <select
                  value={formData.businessInfo?.industry || ''}
                  onChange={(e) => handleBusinessInfoChange('industry', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.industry ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading || isSubmitting}
                  aria-label="مجال الشركة"
                >
                  <option value="">اختر مجال الشركة</option>
                  {BUSINESS_INDUSTRIES.map((industry) => (
                    <option key={industry} value={industry}>
                      {industry}
                    </option>
                  ))}
                </select>
                {errors.industry && (
                  <p className="mt-1 text-sm text-red-600">{errors.industry}</p>
                )}
              </div>

              {/* Company Size */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  حجم الشركة *
                </label>
                <select
                  value={formData.businessInfo?.size || ''}
                  onChange={(e) => handleBusinessInfoChange('size', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.size ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading || isSubmitting}
                  aria-label="حجم الشركة"
                >
                  <option value="">اختر حجم الشركة</option>
                  {BUSINESS_SIZES.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
                {errors.size && (
                  <p className="mt-1 text-sm text-red-600">{errors.size}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-center">
            <p className="text-red-600 dark:text-red-400">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          {/* Back Button */}
          {onBack && (
            <motion.button
              type="button"
              onClick={onBack}
              disabled={isLoading || isSubmitting}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-xl hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              ← العودة
            </motion.button>
          )}

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={isLoading || isSubmitting}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white font-semibold rounded-xl hover:from-primary-700 hover:to-primary-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </div>
            ) : (
              'متابعة إلى المحادثة الذكية'
            )}
          </motion.button>
        </div>
      </form>
    </motion.div>
  );
}
